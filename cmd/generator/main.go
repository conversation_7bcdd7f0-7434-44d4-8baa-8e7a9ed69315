package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/testdata"
)

func main() {
	// 定义命令行参数
	categories := flag.Int("categories", 5, "要生成的分类数量")
	series := flag.Int("series", 3, "要生成的剧集数量")
	resources := flag.Int("resources", 10, "要生成的资源数量")
	flag.Parse()

	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		log.Fatalf("获取当前目录失败: %v", err)
	}

	// 检查是否在正确的目录下运行
	if filepath.Base(wd) == "generator" {
		log.Fatalf("请在项目根目录下运行此程序，而不是在 generator 目录下。\n" +
			"正确的运行方式：\n" +
			"cd ..\n" +
			"go run cmd/generator/main.go -config configs/config.yaml")
	}

	// 加载配置
	conf, err := config.NewConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v\n"+
			"请确保在项目根目录下运行，并使用正确的配置文件路径：\n"+
			"go run cmd/generator/main.go -config configs/config.yaml", err)
	}

	// 初始化数据库连接
	db := model.NewDB(conf)
	if db == nil {
		log.Fatalf("初始化数据库失败")
	}

	// 创建数据库包装器
	dbWrapper := model.NewDBWrapper(db)
	dbModel := model.NewDbModel(dbWrapper)

	// 创建测试数据生成器
	generator := testdata.NewTestDataGenerator(dbModel, conf)

	// 生成测试数据
	fmt.Printf("开始生成测试数据...\n")
	fmt.Printf("分类数量: %d\n", *categories)
	fmt.Printf("剧集数量: %d\n", *series)
	fmt.Printf("资源数量: %d\n", *resources)

	if err := generator.GenerateAll(*categories, *series, *resources); err != nil {
		log.Fatalf("生成测试数据失败: %v", err)
	}

	fmt.Println("测试数据生成完成！")
}
