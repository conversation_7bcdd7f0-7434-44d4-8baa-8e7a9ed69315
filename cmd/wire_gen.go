// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"loop/internal/api"
	"loop/internal/app"
	"loop/internal/client"
	"loop/internal/config"
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/server"
)

// Injectors from wire.go:

func wireApp(config2 *config.Config) (*app.App, func(), error) {
	redisClient, err := client.NewRedisClient(config2)
	if err != nil {
		return nil, nil, err
	}
	ossClient, err := client.NewOssClient(config2)
	if err != nil {
		return nil, nil, err
	}
	clientClient := client.NewClientModel(redisClient, ossClient)
	db := model.NewDB(config2)
	dbExtension := model.NewDBWrapper(db)
	dbModel := model.NewDbModel(dbExtension)
	v1Repo := data.NewV1Repo(dbModel, config2)
	v1Api := api.NewV1Api(v1Repo)
	userModel := model.NewUserModel(dbModel, config2)
	resourceModel := model.NewResourceModel(dbModel, config2)
	videoModel := model.NewVideoModel(dbModel, userModel, resourceModel, config2)
	benefitModel := model.NewBenefitModel(dbModel, config2)
	userRepo := data.NewUserRepo(userModel, resourceModel, videoModel, config2, benefitModel, clientClient)
	settingRepo := data.NewSettingRepo(userModel, resourceModel, config2)
	userApi := api.NewUserApi(userRepo, settingRepo, dbModel)
	userAdminModel := model.NewUserAdminModel(dbModel, config2)
	userAdminRepo := data.NewUserAdminRepo(userAdminModel, config2)
	userAdminApi := api.NewUserAdminApi(userAdminRepo, dbModel)
	homeRepo := data.NewHomeRepo(config2)
	homeApi := api.NewHomeApi(homeRepo)
	categoryModel := model.NewCategoryModel(dbModel, config2)
	seriesModel := model.NewSeriesModel(dbModel, config2)
	resourceRepo := data.NewResourceRepo(resourceModel, seriesModel, config2, clientClient)
	categoryRepo := data.NewCategoryRepo(categoryModel, resourceModel, resourceRepo, config2)
	categoryApi := api.NewCategoryApi(categoryRepo, dbModel)
	categoryAdminModel := model.NewCategoryAdminModel(dbModel, config2)
	categoryAdminRepo := data.NewCategoryAdminRepo(categoryAdminModel, resourceRepo, config2)
	categoryAdminApi := api.NewCategoryAdminApi(categoryAdminRepo, dbModel)
	resourceApi := api.NewResourceApi(resourceRepo, dbModel)
	resourceAdminModel := model.NewResourceAdminModel(dbModel, config2)
	resourceAdminRepo := data.NewResourceAdminRepo(resourceAdminModel, resourceModel, config2)
	resourceAdminApi := api.NewResourceAdminApi(resourceAdminRepo, dbModel)
	dataCenterModel := model.NewDataCenterModel(dbModel, config2)
	noteModel := model.NewNoteModel(dbModel, config2)
	collectModel := model.NewCollectModel(dbModel, config2)
	videoRepo := data.NewVideoRepo(videoModel, dataCenterModel, resourceModel, noteModel, collectModel, userModel, config2, clientClient)
	videoApi := api.NewVideoApi(videoRepo, settingRepo, dbModel)
	notesRepo := data.NewNoteRepo(videoModel, resourceModel, config2)
	noteApi := api.NewNoteApi(notesRepo, dbModel)
	collectRepo := data.NewCollectRepo(collectModel, videoModel, config2)
	collectApi := api.NewCollectApi(collectRepo, settingRepo, dbModel)
	aiRepo := data.NewAIRepo(dbModel, config2)
	aiApi := api.NewAIApi(aiRepo)
	dataCenterRepo := data.NewDataCenterRepo(dataCenterModel, videoModel, resourceModel, userRepo, config2)
	dataCenterApi := api.NewDataCenterApi(dataCenterRepo, dbModel)
	seriesRepo := data.NewSeriesRepo(seriesModel, resourceModel, config2)
	seriesApi := api.NewSeriesApi(seriesRepo, dbModel)
	seriesAdminModel := model.NewSeriesAdminModel(dbModel, config2)
	seriesAdminRepo := data.NewSeriesAdminRepo(seriesAdminModel, resourceAdminModel, seriesModel, config2)
	seriesAdminApi := api.NewSeriesAdminApi(seriesAdminRepo, dbModel)
	vipModel := model.NewVipModel(dbModel, config2)
	vipRepo := data.NewVipRepo(vipModel, config2)
	vipApi := api.NewVipApi(vipRepo)
	benefitRepo := data.NewBenefitRepo(db, userRepo, benefitModel)
	benefitApi := api.NewBenefitApi(benefitRepo, benefitModel)
	planModel := model.NewPlanModel(dbModel, config2)
	planRepo := data.NewPlanRepo(planModel, resourceModel, config2, clientClient)
	planApi := api.NewPlanApi(planRepo)
	engine := server.NewRegisterHTTPServer(config2, clientClient, v1Api, userApi, userAdminApi, homeApi, categoryApi, categoryAdminApi, resourceApi, resourceAdminApi, videoApi, noteApi, collectApi, aiApi, dataCenterApi, seriesApi, seriesAdminApi, vipApi, benefitApi, planApi)
	httpServer := server.NewHTTPServer(config2, engine)
	appApp := newApp(httpServer, db, redisClient)
	return appApp, func() {
	}, nil
}
