package data

import (
	"fmt"
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/jwtx"
	"loop/pkg/web"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"golang.org/x/crypto/bcrypt"
)

func NewUserAdminRepo(
	model *model.UserAdminModel,
	config *config.Config,
) *UserAdminRepo {
	return &UserAdminRepo{
		model:  model,
		config: config,
	}
}

type UserAdminRepo struct {
	model  *model.UserAdminModel
	config *config.Config
}

func (s *UserAdminRepo) LoginAdmin(c *gin.Context, req request.UserAdminLoginReq) *web.JsonResult {
	user, err := s.model.FindAdminByUsername(req.Username)
	if err != nil {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, err.Error())
	}
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordDigest), []byte(req.Password))
	if err != nil {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, err.Error())
	}
	token, err := jwtx.GenerateAdminToken(fmt.Sprintf("%d", user.Id), req.Username, user.RoleLevel, user.Status, s.config.JwtAdmin.SignKey, int64(s.config.JwtAdmin.ExpireSeconds))
	if err != nil {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, err.Error())
	}
	userResp := response.LoginAdminUserInfoResp{
		User: &response.UserAdminInfoResp{},
	}
	userResp.Token = token
	copier.Copy(&userResp.User, &user)
	return web.JsonData(userResp)
}

func (s *UserAdminRepo) GetUserList(c *gin.Context, req request.ListReq) *web.JsonResult {
	var users []*model.User

	count, err := s.model.GetListPage(&users, req.CurrentPage, req.PageSize, "")
	if err != nil {
		return web.JsonInternalError(err)
	}
	var resps []response.UserAdminInfoResp
	copier.Copy(&resps, users)
	return web.JsonData(web.PageJsonResult{
		Data:  resps,
		Total: count,
	})
}
