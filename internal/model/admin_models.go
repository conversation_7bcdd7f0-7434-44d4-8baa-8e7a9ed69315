package model

type SysUser struct {
	Model
	UserName       string `gorm:"size:64;not null;column:username;comment:用户名"`
	PasswordDigest string `gorm:"size:128;not null;comment:密码"`
	RoleLevel      int    `gorm:"size:20;not null;comment:等级"`
	Status         int    `gorm:"size:20;default:0;comment:状态 0正常 1禁用"`
}

func (SysUser) TableName() string {
	return "sys_users"
}

type SysRole struct {
	Model
	Name      string `gorm:"size:64;not null;column:name;comment:角色名称"`
	RoleLevel int    `gorm:"size:20;not null;comment:等级"`
}

func (SysRole) TableName() string {
	return "sys_roles"
}
