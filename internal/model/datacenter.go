package model

import (
	"loop/internal/config"
	"loop/internal/response"
	"time"

	"errors"
	"loop/pkg/util"

	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

// 用户每个剧集的数据
type DataEpisode struct {
	Model
	Uid                     string `gorm:"not null;index:idx_user_watch,priority:1" json:"uid"`
	ResourceId              string `gorm:"type:varchar(20)"`       // 视频资源ID
	ResourceType            int    `gorm:"type:tinyint;default:0"` //视频类型 1代表远程的资源 2代表本地资源
	Status                  int    `gorm:"size:2;not null"`        ////0进行中、1已完成
	TargetLsTimes           int64  `gorm:"type:bigint;default:0"`  //目标LS次数，默认100
	CurrentLsTimes          int64  `gorm:"type:bigint;default:0"`
	LearnDurationWhenFinish int64  `gorm:"type:bigint;default:0"` //完成LS的时候完成的分钟数 需要固定
	StartTime               int64  `gorm:"type:bigint;default:0"` //剧集LS开始学习时间
	EndTime                 int64  `gorm:"type:bigint;default:0"` //剧集LS完成学习时间,可以为空
}

func (DataEpisode) TableName() string {
	return "data_episodes"
}

// 用户每次的学习数据
type DataEpisodeEach struct {
	ModelAutoId
	Uid            string `gorm:"not null;index:idx_user_watch,priority:1" json:"uid"`
	ResourceId     string `gorm:"type:varchar(20)"`       // 视频资源ID
	ResourceType   int    `gorm:"type:tinyint;default:0"` //视频类型 1代表远程的资源 2代表本地资源
	CurrentLsTimes int64  `gorm:"type:bigint;default:0"`  //当前是第几遍LS，用来计算每个LS遍数的统计
	LearnDuration  int64  `gorm:"type:bigint;default:0"`  //单次的学习时长，可能和开始结束时间差会不一样，因为可以中途退出"`
	StartTime      int64  `gorm:"type:bigint;default:0"`  //`每次记录的开始时间
	EndTime        int64  `gorm:"type:bigint;default:0"`  // 每次记录的结束时间
	SentencesJSON  string `gorm:"type:text"`
}

func (DataEpisodeEach) TableName() string {
	return "data_episode_eachs"
}
func NewDataCenterModel(dbModel *DbModel, config *config.Config) *DataCenterModel {
	return &DataCenterModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type DataCenterModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *DataCenterModel) GetDataEpisodeAndCreate(uid string, resourceId string, resourceType int) (*DataEpisode, error) {
	query := DataEpisode{Uid: uid, ResourceId: resourceId, ResourceType: resourceType}
	result := DataEpisode{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}
		logrus.Info("User ", uid, "  create newDataEpisode")
		now := time.Now().Unix()
		newDataEpisode := DataEpisode{
			Uid:            uid,
			ResourceId:     resourceId,
			ResourceType:   resourceType,
			Status:         0,
			TargetLsTimes:  100,
			CurrentLsTimes: 1,
			StartTime:      now,
		}
		if err := r.SaveOne(&newDataEpisode); err != nil {
			return nil, err
		}
		return &result, nil
	}
	return &result, nil
}

func (r *DataCenterModel) GetDataEpisode(uid string, resourceId string, resourceType int) (*DataEpisode, error) {
	query := DataEpisode{Uid: uid, ResourceId: resourceId, ResourceType: resourceType}
	var result DataEpisode
	err := r.GetList(&result, query)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
func (r *DataCenterModel) GetDataEpisodeList(uid string) ([]DataEpisode, error) {
	query := DataEpisode{Uid: uid}
	var result []DataEpisode
	err := r.GetOrderedList(&result, "updated_at DESC", query)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *DataCenterModel) GetTodayDataEpisodeDailys(uid string, resourceId string, resourceType int) ([]*DataEpisodeEach, error) {
	now := time.Now()
	location := now.Location()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)
	endOfDay := startOfDay.Add(24 * time.Hour)
	startTimestamp := startOfDay.Unix()
	endTimestamp := endOfDay.Unix()
	result := []*DataEpisodeEach{}
	err := r.GetList(&result, "uid = ? AND resource_id = ? AND resource_type = ? AND start_time >= ? AND end_time < ?", uid, resourceId, resourceType, startTimestamp, endTimestamp)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *DataCenterModel) GetWeekDataEpisode(uid string, start, end time.Time) (map[int][]*DataEpisodeEach, error) {

	// Prepare a map to store data by day
	weekData := make(map[int][]*DataEpisodeEach)

	episodes, err := r.GetDataEpisodeByDate(uid, start, end)
	if err != nil {
		return nil, err
	}

	// Organize the data by day
	for _, episode := range episodes {
		weekDay := time.Unix(episode.StartTime, 0).Weekday()
		day := (int(weekDay) + 6) % 7 // Map Monday=0, ..., Sunday=6
		weekData[day] = append(weekData[day], episode)
	}

	return weekData, nil
}

func (r *DataCenterModel) GetDayDataEpisode(uid string, start, end time.Time) (map[int][]*DataEpisodeEach, error) {

	dayData := make(map[int][]*DataEpisodeEach)
	episodes, err := r.GetDataEpisodeByDate(uid, start, end)
	if err != nil {
		return nil, err
	}

	for _, episode := range episodes {
		hour := time.Unix(episode.StartTime, 0).Hour()
		dayData[hour] = append(dayData[hour], episode)
	}

	return dayData, nil
}
func (r *DataCenterModel) GetMonthDataEpisode(uid string, start, end time.Time) (map[int][]*DataEpisodeEach, error) {

	monthData := make(map[int][]*DataEpisodeEach)
	episodes, err := r.GetDataEpisodeByDate(uid, start, end)
	if err != nil {
		return nil, err
	}

	for _, episode := range episodes {
		day := time.Unix(episode.StartTime, 0).Day()
		monthData[day] = append(monthData[day], episode)
	}

	return monthData, nil
}
func (r *DataCenterModel) GetYearDataEpisode(uid string, start, end time.Time) (map[int][]*DataEpisodeEach, error) {
	yearData := make(map[int][]*DataEpisodeEach)
	episodes, err := r.GetDataEpisodeByDate(uid, start, end)
	if err != nil {
		return nil, err
	}

	for _, episode := range episodes {
		month := time.Unix(episode.StartTime, 0).Month()
		yearData[int(month)] = append(yearData[int(month)], episode)
	}

	return yearData, nil
}
func (r *DataCenterModel) GetDataEpisodeByDate(uid string, start, end time.Time) ([]*DataEpisodeEach, error) {
	var episodes []*DataEpisodeEach
	err := r.Where("uid = ? AND start_time >= ? AND start_time < ?", uid, start.Unix(), end.Unix()).Find(&episodes).Error
	if err != nil {
		return nil, err
	}
	return episodes, nil
}

type TotalDataEpisodeResult struct {
	TotalLearnDuration int64
	DayCount           int64
}

func (r *DataCenterModel) GetTotalDataEpisode(uid string, resourceId string, resourceType int) (*TotalDataEpisodeResult, error) {
	return r.GetTotalDataEpisodeByTime(uid, resourceId, resourceType, nil, nil)
}

func (r *DataCenterModel) GetTotalDataEpisodeByTime(uid string, resourceId string, resourceType int, start, end *time.Time) (*TotalDataEpisodeResult, error) {
	// 参数验证
	if !util.IsValidID(uid) {
		return nil, errors.New("invalid uid format")
	}
	if resourceId != "" && !util.IsValidID(resourceId) {
		return nil, errors.New("invalid resource id format")
	}

	var result TotalDataEpisodeResult

	// 使用更安全的日期处理方式
	query := r.Model(&DataEpisodeEach{}).
		Select("SUM(learn_duration) AS total_learn_duration, COUNT(DISTINCT DATE(FROM_UNIXTIME(start_time))) AS day_count").
		Where("uid = ? AND DATE(FROM_UNIXTIME(start_time)) = DATE(FROM_UNIXTIME(end_time))", uid)

	if start != nil && end != nil {
		query = query.Where("start_time >= ? AND start_time < ?", start.Unix(), end.Unix())
	}

	if resourceId != "" {
		query = query.Where("resource_id = ?", resourceId)
	}

	if resourceType != 0 {
		query = query.Where("resource_type = ?", resourceType)
	}

	// 添加日志
	logrus.WithFields(logrus.Fields{
		"operation":  "GetTotalDataEpisodeByTime",
		"uid":        uid,
		"resourceId": resourceId,
		"timestamp":  time.Now(),
	}).Info("Data Query Log")

	err := query.Scan(&result).Error
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *DataCenterModel) GetEpisodeLsData(uid string, resourceId string, resourceType int) ([]response.EpisodeLsData, error) {
	var results []response.EpisodeLsData

	// 查询每个 CurrentLsTimes 的 LearnDuration 总和，并获取最新的结束时间。
	query := r.Model(&DataEpisodeEach{}).
		Select("current_ls_times AS ls_times, SUM(learn_duration) AS total_learn_duration, MAX(end_time) AS finish_time").
		Where("uid = ?", uid).
		Group("current_ls_times").
		Order("ls_times DESC") // 添加排序

	if resourceId != "" {
		query = query.Where("resource_id = ?", resourceId)
	}

	if resourceType != 0 {
		query = query.Where("resource_type = ?", resourceType)
	}

	err := query.Scan(&results).Error

	if err != nil {
		return nil, err
	}
	return results, nil
}
