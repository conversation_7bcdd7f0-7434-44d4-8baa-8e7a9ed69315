package model

import (
	"fmt"
	"loop/internal/config"
	"loop/internal/request"
	"loop/pkg/dbx"
	"loop/pkg/enum"
	"sync"

	"github.com/jinzhu/copier"
	"github.com/thoas/go-funk"
	"go4.org/syncutil/singleflight"
)

func NewResourceAdminModel(dbModel *DbModel, config *config.Config) *ResourceAdminModel {
	return &ResourceAdminModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type ResourceAdminModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *ResourceAdminModel) GetById(id string) (*Resource, error) {
	query := Resource{Model: Model{Id: id}}
	result := Resource{}
	if found, err := r.GetOne(&result, query); !found {
		return nil, err
	}
	return &result, nil
}

func (r *ResourceAdminModel) CheckCategoryIds(categoryIds []uint) error {
	var categories []Category
	// 查询所有提供的分类 ID
	if err := r.Where("id IN ?", categoryIds).Find(&categories).Error; err != nil {
		return err
	}

	// 创建一个 map 来跟踪找到的分类 ID
	foundIds := make(map[uint]bool)
	for _, category := range categories {
		foundIds[category.Id] = true
	}

	// 检查是否有未找到的分类 ID
	for _, id := range categoryIds {
		if !foundIds[id] {
			return fmt.Errorf("category ID not found: %d", id)
		}
	}

	return nil
}
func (r *ResourceAdminModel) DeleteResources(resourceIds []string) error {
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("id IN ?", resourceIds).Delete(&Resource{}).Error
		},
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("resource_id IN ?", resourceIds).Delete(&ResourceRelation{}).Error
		},
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("resource_id IN ?", resourceIds).Delete(&CategoryResourceRelations{}).Error
		},
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("resource_id IN ?", resourceIds).Delete(&SeriesResourceRelations{}).Error
		},
		func(txDb *dbx.DBExtension) error {
			return txDb.Where("content_id IN ? AND content_type = ?", resourceIds, int(enum.Resource)).Delete(&FeaturedContent{}).Error
		},
	)
	return err
}
func (r *ResourceAdminModel) AddResource(req request.ResourceAddReq) error {
	var resourceId = req.Id
	var isUpdate = req.Id != ""
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			if isUpdate {
				if found, _ := txDb.GetOne(&Resource{}, Resource{Model: Model{Id: req.Id}}); !found {
					isUpdate = false
				}
			}
			if isUpdate {
				if err := txDb.Delete(&ResourceRelation{}, ResourceRelation{ResourceId: resourceId}).Error; err != nil {
					return err
				}
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			if isUpdate {
				resourceId = req.Id
			}
			var rrSaver ResourceRelation
			copier.Copy(&rrSaver, req.OriginResourceRelation)
			rrSaver.ResourceId = resourceId
			if err := txDb.Save(&rrSaver).Error; err != nil {
				return err
			}
			originResourceRelationId := rrSaver.Id
			//插入数据到资源
			var saver Resource
			copier.Copy(&saver, &req)
			saver.OriginReourcelationId = originResourceRelationId
			if isUpdate {
				if err := txDb.Update(&saver, "id = ?", req.Id); err != nil {
					return err
				}
			} else {
				if err := txDb.Save(&saver).Error; err != nil {
					return err
				}
				resourceId = saver.Id
				rrSaver.ResourceId = resourceId
				if err := txDb.Update(&rrSaver, "id = ?", rrSaver.Id); err != nil {
					return err
				}
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {

			// 准备插入数据
			var resourceRelations []ResourceRelation
			for _, item := range req.ResourceRelations {
				saver := ResourceRelation{
					ResourceId:  resourceId,
					LangCode:    item.LangCode,
					Title:       item.Title,
					Description: item.Description,
					SubtitleUrl: item.SubtitleUrl,
				}

				resourceRelations = append(resourceRelations, saver)
			}

			// 批量插入
			if err := txDb.CreateInBatches(resourceRelations, len(resourceRelations)).Error; err != nil {
				return err
			}
			return nil
		},

		func(txDb *dbx.DBExtension) error {
			var entries []CategoryResourceRelations
			if isUpdate {
				if err := txDb.Delete(&CategoryResourceRelations{}, CategoryResourceRelations{ResourceId: resourceId}).Error; err != nil {
					return err
				}
			}
			if len(req.CategoryIds) == 0 {
				return nil
			}
			for _, categoryId := range req.CategoryIds {
				entries = append(entries, CategoryResourceRelations{
					ResourceId: resourceId,
					CategoryId: categoryId,
				})
			}

			if err := txDb.CreateInBatches(entries, len(entries)).Error; err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {

			var entries []SeriesResourceRelations
			if isUpdate {
				if err := txDb.Delete(&SeriesResourceRelations{}, SeriesResourceRelations{ResourceId: resourceId}).Error; err != nil {
					return err
				}
			}
			if len(req.SeriesIds) == 0 {
				return nil
			}
			for _, seriesId := range req.SeriesIds {
				entries = append(entries, SeriesResourceRelations{
					ResourceId: resourceId,
					SeriesId:   seriesId,
				})
			}

			if err := txDb.CreateInBatches(entries, len(entries)).Error; err != nil {
				return err
			}
			return nil
		},
	)
	return err
}

type ResourceComplete struct {
	Resource
	ResourceRelations      []ResourceRelation
	OriginResourceRelation ResourceRelation
	Categories             []Category
	Serieses               []Series
}

func (r *ResourceAdminModel) GetResourceDetail(resourceID string) (ResourceComplete, error) {
	var response ResourceComplete
	var resource Resource
	var err error
	var resourceRelations []ResourceRelation
	var originResourceRelation ResourceRelation

	var categories []Category
	var serieses []Series

	var wg sync.WaitGroup
	wg.Add(3)
	go func() {
		defer wg.Done()
		_, err = r.GetOne(&resource, Resource{Model: Model{Id: resourceID}})
		var allResourceRelations []ResourceRelation
		err = r.Where(ResourceRelation{ResourceId: resourceID}).Find(&allResourceRelations).Error
		resourceRelations = funk.Filter(allResourceRelations, func(r ResourceRelation) bool {
			return r.Id != resource.OriginReourcelationId
		}).([]ResourceRelation)
		originResourceRelation, _ = funk.Find(allResourceRelations, func(r ResourceRelation) bool {
			return r.Id == resource.OriginReourcelationId
		}).(ResourceRelation)
	}()
	go func() {
		defer wg.Done()
		var seriesResources []SeriesResourceRelations
		err = r.Where(SeriesResourceRelations{ResourceId: resourceID}).Find(&seriesResources).Error
		seriesIds := make([]string, len(seriesResources))
		for i, rc := range seriesResources {
			seriesIds[i] = rc.SeriesId
		}

		err = r.Where("id IN (?)", seriesIds).Find(&serieses).Error
	}()
	go func() {
		defer wg.Done()
		var resourceCategories []CategoryResourceRelations
		err = r.Where(CategoryResourceRelations{ResourceId: resourceID}).Find(&resourceCategories).Error
		categoryIDs := make([]uint, len(resourceCategories))
		for i, rc := range resourceCategories {
			categoryIDs[i] = rc.CategoryId
		}

		err = r.Where("id IN (?)", categoryIDs).Find(&categories).Error
	}()

	wg.Wait()

	if err != nil {
		return response, err
	}
	response.Resource = resource
	response.ResourceRelations = resourceRelations
	response.OriginResourceRelation = originResourceRelation
	response.Categories = categories
	response.Serieses = serieses
	return response, nil

}
