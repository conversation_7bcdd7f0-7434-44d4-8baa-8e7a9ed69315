package model

import (
	"context"
	"fmt"
	"loop/internal/config"
	"loop/internal/constants"
	"loop/internal/request"
	"loop/pkg/dbx"
	"loop/pkg/types"

	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

type User struct {
	Model
	Username            string `gorm:"size:64;not null;column:username;comment:用户名"`
	PasswordDigest      string `gorm:"size:128;comment:密码"`
	Nickname            string `gorm:"size:128;default:null;comment:昵称"`
	Status              int    `gorm:"size:20;default:0;comment:状态 0正常 1禁用 2已注销"`
	AppleUserIdentifier string `gorm:"size:128;comment:苹果登录的唯一码"`
	Avatar              string `gorm:"size:1000;comment:用户头像"`
	NativeLangCode      string `gorm:"size:128;comment:用户母语"`
	TargetLangCode      string `gorm:"size:128;comment:用户想要学习的语言"`
	VipLevelId          string `gorm:"size:20;comment:VIP等级ID"`
}

func (User) TableName() string {
	return "users"
}

// 用户的播放器配置
type UserPlayerConfig struct {
	Model
	Uid                           string         `gorm:"not null;index:idx_user_watch,priority:1"`
	SubtitleTextBottomHeightRatio float32        `gorm:"size:20;not null;comment:字幕文件底部距离与整个播放器的比例"`
	ShowSubtitleWhenRecordEnd     bool           `gorm:"size:20;not null;comment:录制结束后显示字幕"`
	AutoPlayRecordWhenRecordEnd   bool           `gorm:"size:20;not null;comment:录制结束后自动播放录制声音"`
	AutoRecord                    bool           `gorm:"size:20;not null;comment:自动开始录制"`
	AutoStopRecord                bool           `gorm:"size:20;not null;comment:自动停止录制"`
	OpenSingleRepeat              bool           `gorm:"size:20;not null;comment:单句循环播放"`
	SubtitleFontSize              int            `gorm:"size:20;not null;comment:字幕文字大小"`
	SingleRepeatCount             int            `gorm:"size:20;not null;comment:单句重复次数,0 代表 无限次数"`
	ShowSubtitleNum               int            `gorm:"size:20;not null;comment:是否显示字幕序号0不显示1显示"`
	CoverSubtitle                 bool           `gorm:"size:20;not null;comment:是否打开字幕遮挡"`
	MenuSort                      types.IntArray `gorm:"type:json;not null;comment:播放器里的菜单顺序,为空就是默认"`
}

func (UserPlayerConfig) TableName() string {
	return "user_player_configs"
}

// WatchHistory 定义了用户观看历史的结构体
type WatchHistory struct {
	Model
	Uid          string `gorm:"not null;index:idx_user_watch,priority:1"`
	ResourceId   string `gorm:"not null"`               // 视频资源ID
	ResourceType int    `gorm:"type:tinyint;default:0"` //视频类型 1代表远程的资源 2代表本地资源
	Position     int64  `gorm:"type:bigint;default:0"`  // 用户停止观看的位置
}

func (WatchHistory) TableName() string {
	return "watch_historys"
}

func NewUserModel(dbModel *DbModel, config *config.Config) *UserModel {
	return &UserModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type UserModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *UserModel) GetUserByUId(ctx context.Context, uid string) (*User, error) {
	res, err := r.sg.Do(fmt.Sprintf("get_user_by_uid_%s", uid), func() (interface{}, error) {
		query := User{Model: Model{Id: uid}}
		user := User{}
		if found, err := r.GetOne(&user, query); !found {
			return nil, err
		}
		return &user, nil
	})
	if err != nil {
		return nil, err
	}
	data, ok := res.(*User)
	if !ok {
		return nil, fmt.Errorf("type assertion to *DATA failed")
	}
	return data, nil
}

func (r *UserModel) FindByUsername(username string) (*User, error) {
	query := User{Username: username}
	user := User{}
	if found, err := r.GetOne(&user, query); !found {
		return nil, err
	}
	return &user, nil
}

func (r *UserModel) FindByAppleSign(appleUserIdentifier string) (*User, error) {
	query := User{AppleUserIdentifier: appleUserIdentifier}
	user := User{}
	found, err := r.GetOne(&user, query)
	if err != nil {
		return nil, err
	}
	if !found {
		return nil, nil
	}
	return &user, nil
}
func (r *UserModel) GetPlayerConfig(uid string) (*UserPlayerConfig, error) {
	query := UserPlayerConfig{Uid: uid}
	result := UserPlayerConfig{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	return &result, nil
}
func (r *UserModel) AddOrUpdateWatchHistory(uid string, req request.WatchHistoryAddReq) error {
	query := WatchHistory{Uid: uid, ResourceId: req.ResourceId, ResourceType: req.ResourceType}
	saver := WatchHistory{}
	found, err := r.GetOne(&saver, query)
	if err != nil {
		return err
	}
	saver.Position = req.Position
	if found {
		logrus.Info("User ", uid, " update WatchHistory id=", saver.Id)
		if err := r.Update(&saver, "id = ?", saver.Id); err != nil {
			return err
		}
	} else {
		logrus.Info("User ", uid, " save WatchHistory")
		copier.Copy(&saver, &query)
		err = r.SaveOne(&saver)
		if err != nil {
			return err
		}
	}

	return nil
}
func (r *UserModel) DeleteWatchHistory(uid string, resourceId string, resourceType int) error {
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			if err := txDb.Delete(&WatchHistory{}, WatchHistory{Uid: uid, ResourceId: resourceId, ResourceType: resourceType}).Error; err != nil {
				return err
			}
			return nil
		},
	)
	return err

}
func (r *UserModel) RegisterUser(user User, langCode string) (*User, error) {
	var newUser User
	copier.Copy(&newUser, &user)
	newUser.NativeLangCode = langCode
	if err := r.SaveOne(&newUser); err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserModel) DeleteAccount(uid string) error {
	return r.Tx(
		func(txDb *dbx.DBExtension) error {
			// 1. 更新用户状态为已删除
			user := User{Model: Model{Id: uid}}
			if err := txDb.Model(&user).Update("status", constants.UserStatusDeleted).Error; err != nil {
				return err
			}

			// 2. 删除用户订阅信息
			if err := txDb.Model(&UserSubscription{}).Where(&UserSubscription{Uid: uid}).Updates(&UserSubscription{
				Status: UserSubscriptionStatusTermination,
				Desc:   "用户主动删除账号",
			}).Error; err != nil {
				logrus.Errorf("删除用户订阅信息异常: uid=%s, err=%v", uid, err)
				return err
			}

			// 3. 删除用户会员关系
			if err := txDb.Delete(&UserVIPRelations{}).Where(&UserVIPRelations{Uid: uid}).Error; err != nil {
				logrus.Errorf("删除用户会员关系: uid=%s, err=%v", uid, err)
				return err
			}

			// 4. 删除用户配置信息
			if err := txDb.Delete(&UserPlayerConfig{}, &UserPlayerConfig{Uid: uid}).Error; err != nil {
				return err
			}

			// 5. 删除观看历史
			if err := txDb.Delete(&WatchHistory{}, &WatchHistory{Uid: uid}).Error; err != nil {
				return err
			}

			// 6. 删除字幕关系
			if err := txDb.Delete(&UserSubtitleRelations{}, &UserSubtitleRelations{Uid: uid}).Error; err != nil {
				return err
			}

			// 7. 删除本地资源关系
			if err := txDb.Delete(&UserLocalResource{}, &UserLocalResource{Uid: uid}).Error; err != nil {
				return err
			}

			// 8. 删除笔记收藏关系
			if err := txDb.Delete(&NoteCollectRelations{}, &NoteCollectRelations{Uid: uid}).Error; err != nil {
				return err
			}

			// 9. 删除远程资源关系
			if err := txDb.Delete(&UserRemoteResourceRelations{}, &UserRemoteResourceRelations{Uid: uid}).Error; err != nil {
				return err
			}

			// 10. 删除学习数据
			if err := txDb.Delete(&DataEpisode{}, &DataEpisode{Uid: uid}).Error; err != nil {
				return err
			}
			if err := txDb.Delete(&DataEpisodeEach{}, &DataEpisodeEach{Uid: uid}).Error; err != nil {
				return err
			}

			return nil
		},
	)
}
