package request

// UserQuestionnaireReq represents the request for submitting a questionnaire
type UserQuestionnaireReq struct {
	MotivationSource  string `json:"motivationSource" binding:"required" form:"motivationSource"`
	DesiredAbility    string `json:"desiredAbility" binding:"required" form:"desiredAbility"`
	CurrentLevel      string `json:"currentLevel" binding:"required" form:"currentLevel"`
	TargetLevel       string `json:"targetLevel" binding:"required" form:"targetLevel"`
	DailyStudyMinutes int    `json:"dailyStudyMinutes" binding:"required,min=1" form:"dailyStudyMinutes"`
}

// GeneratePlanReq 表示生成学习计划的请求
type GeneratePlanReq struct {
	// 可选参数，如果不提供则使用用户的问卷数据
	CurrentLevel      string `json:"currentLevel" form:"currentLevel"`           // 当前级别
	TargetLevel       string `json:"targetLevel" form:"targetLevel"`             // 目标级别
	DailyStudyMinutes int    `json:"dailyStudyMinutes" form:"dailyStudyMinutes"` // 每日学习时间（分钟）
	MotivationSource  string `json:"motivationSource" form:"motivationSource"`   // 动力来源
	DesiredAbility    string `json:"desiredAbility" form:"desiredAbility"`       // 期望能力
	ForceRegenerate   bool   `json:"forceRegenerate" form:"forceRegenerate"`     // 是否强制重新生成计划
}

// GetPlanReq 表示获取学习计划的请求
type GetPlanReq struct {
	// 主要使用JWT令牌中的用户ID
	PlanId uint `json:"planId" form:"planId"` // 计划ID，可选，如果不提供则获取用户当前活跃的计划
}

// InvalidatePlanReq 表示使学习计划失效的请求
type InvalidatePlanReq struct {
	PlanId uint   `json:"planId" form:"planId"` // 计划ID，可选，如果不提供则使所有活跃计划失效
	Reason string `json:"reason" form:"reason"` // 使计划失效的原因，可选
}

// GetLearningLevelsReq 表示获取学习级别的请求
type GetLearningLevelsReq struct {
	ParentCode string `json:"parentCode" form:"parentCode"` // 父级别代码，可选，如果提供则只返回该父级别下的子级别
	IncludeAll bool   `json:"includeAll" form:"includeAll"` // 是否包含所有级别，默认为false，只返回主要级别
}
