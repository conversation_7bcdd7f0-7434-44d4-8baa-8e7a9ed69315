package request

type SeriesAddReq struct {
	Id              string              `form:"id" json:"id"`
	Cover           string              `form:"cover" json:"cover"`
	SeriesRelations []SeriesRelationReq `json:"seriesRelations" binding:"required"`
	CategoryIds     []uint              `json:"categoryIds" binding:"required"`
}

// 剧集多语言信息
type SeriesRelationReq struct {
	LangCode    string `form:"langCode" json:"langCode" binding:"required"`
	Title       string `json:"title" binding:"required"`
	Description string `json:"description" binding:"required"`
	Statement   string `json:"statement"  binding:"required"`
}
