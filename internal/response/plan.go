package response

// UserQuestionnaireResp represents the response for a user's questionnaire
type UserQuestionnaireResp struct {
	Id                uint   `json:"id"`
	MotivationSource  string `json:"motivationSource"`
	DesiredAbility    string `json:"desiredAbility"`
	CurrentLevel      string `json:"currentLevel"`
	TargetLevel       string `json:"targetLevel"`
	DailyStudyMinutes int    `json:"dailyStudyMinutes"`
	CreatedAt         string `json:"createdAt"`
	UpdatedAt         string `json:"updatedAt"`
}

// LearningLevelResp represents a learning level
type LearningLevelResp struct {
	Id          uint   `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ParentCode  string `json:"parentCode,omitempty"`
}

// PlanResourceResp represents a resource in a learning plan
type PlanResourceResp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceCover string `json:"resourceCover,omitempty"`
	LsCount       int    `json:"lsCount"`
}

// PlanWeekResourceResp represents a resource in a weekly plan
type PlanWeekResourceResp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceCover string `json:"resourceCover,omitempty"`
	LsCount       int    `json:"lsCount"`
}

// PlanWeekResp represents a week in a learning plan stage
type PlanWeekResp struct {
	WeekNumber int                    `json:"weekNumber"`
	Resources  []PlanWeekResourceResp `json:"resources"`
}

// PlanStageResp represents a stage in a learning plan
type PlanStageResp struct {
	Id        uint           `json:"id"`
	StageDesc string         `json:"stageDesc"` // 阶段描述，如 A0-1, A1-2 等
	Objective string         `json:"objective"`
	StartDate string         `json:"startDate,omitempty"`
	EndDate   string         `json:"endDate,omitempty"`
	Weeks     []PlanWeekResp `json:"weeks,omitempty"` // 按周划分的学习计划
}

// LearningPlanResp represents a learning plan
type LearningPlanResp struct {
	Id          uint            `json:"id"`
	StartLevel  string          `json:"startLevel"`
	TargetLevel string          `json:"targetLevel"`
	StartDate   string          `json:"startDate"`
	EndDate     string          `json:"endDate,omitempty"`
	Status      int             `json:"status"`
	Stages      []PlanStageResp `json:"stages"`
	CreatedAt   string          `json:"createdAt"`
}

// PlanSummaryResp represents a summary of a learning plan
type PlanSummaryResp struct {
	Id          uint   `json:"id"`
	StartLevel  string `json:"startLevel"`
	TargetLevel string `json:"targetLevel"`
	StartDate   string `json:"startDate"`
	EndDate     string `json:"endDate,omitempty"`
	Status      int    `json:"status"`
	StageCount  int    `json:"stageCount"`
	CreatedAt   string `json:"createdAt"`
}
