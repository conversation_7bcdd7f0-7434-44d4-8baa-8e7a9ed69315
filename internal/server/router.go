package server

import (
	"loop/internal/api"
	"loop/internal/client"
	"loop/internal/config"
	"loop/middleware"
	"loop/pkg/util"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

func NewRegisterHTTPServer(
	config *config.Config,
	client *client.Client,
	v1Api *api.V1Api,
	userApi *api.UserApi,
	userAdminApi *api.UserAdminApi,
	homeApi *api.HomeApi,
	lsResCategoryApi *api.CategoryApi,
	categoryAdminApi *api.CategoryAdminApi,
	lsResourceApi *api.ResourceApi,
	resourceAdminApi *api.ResourceAdminApi,
	videoApi *api.VideoApi,
	notesApi *api.NoteApi,
	collectApi *api.CollectApi,
	aiApi *api.AIApi,
	datacenterApi *api.DataCenterApi,
	seriesApi *api.SeriesApi,
	seriesAdminApi *api.SeriesAdminApi,
	vipApi *api.VipApi,
	benefitApi *api.BenefitApi,
	planApi *api.PlanApi,
) *gin.Engine {
	r := gin.Default()
	r.Use(middleware.CircuitBreakerMiddleware())
	if config.Limit.EnableIpLimit {
		r.Use(middleware.IPLimitMiddleware)
	}
	if config.Limit.EnableUserLimit {
		r.Use(middleware.UserLimitMiddleware)
	}
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		util.RegisterCustomValidators(v)
	}
	// 在所有模式下都启用日志中间件
	r.Use(middleware.LoggerToFile(), gin.Recovery())

	// 为 multipart forms 设置较低的内存限制 (默认是 32 MiB)
	r.MaxMultipartMemory = 8 << 20 // 8 MiB
	// 中间件, 顺序不能改
	r.Use(middleware.Cors())

	// 路由
	v1 := r.Group("/api/v1")
	{
		// 创建统一的路由组
		protected := v1.Group("/")
		protected.Use(middleware.JWT(config.Jwt.SignKey, client))
		// protected.Use(middleware.JWT(config.JwtAdmin.SignKey))

		adminProtected := v1.Group("/admin/")
		adminProtected.Use(middleware.AdminCheck(config.JwtAdmin.SignKey))

		// 注册各个模块的路由
		addV1(config, v1Api, v1, protected, adminProtected)
		addAdmin(config, v1Api, v1, protected, adminProtected)
		addUser(config, userApi, userAdminApi, v1, protected, adminProtected)
		addCategory(config, lsResCategoryApi, categoryAdminApi, v1, protected, adminProtected)
		addResource(config, lsResourceApi, resourceAdminApi, v1, protected, adminProtected)
		addVideo(config, videoApi, v1, protected)
		addNotes(config, notesApi, v1, protected)
		addCollect(config, collectApi, v1, protected)
		addDataCenter(config, datacenterApi, v1, protected)
		addSeries(config, seriesApi, seriesAdminApi, v1, protected, adminProtected)
		addVip(config, vipApi, v1, protected, adminProtected)
		addBenefit(config, benefitApi, v1, protected, adminProtected)
		addPlan(config, planApi, v1, protected)
	}

	return r
}

func addV1(
	config *config.Config,
	v1Api *api.V1Api,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
	adminProtected *gin.RouterGroup,
) {
	rg.GET("teds", v1Api.GetTedSource)
	//本地语言 母语 包括app端可以设置的语言
	rg.GET("langs", v1Api.GetLangs)
	rg.GET("nativeLangs", v1Api.GetNativeLangs)
	//可以学习的语言 代表有这个语言的视频资源 app中可以设置想要学习的语言
	rg.GET("targetLangs", v1Api.GetTargetLangs)

	adminProtected.GET("nativeLangs", v1Api.GetNativeLangs)
	adminProtected.GET("targetLangs", v1Api.GetTargetLangs)
}

func addCategory(
	config *config.Config,
	categoryApi *api.CategoryApi,
	categoryAdminApi *api.CategoryAdminApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
	adminProtected *gin.RouterGroup,
) {
	protected.GET("category", categoryApi.GetById)

	adminProtected.GET("categorys", categoryAdminApi.GetCategoryList)
	adminProtected.POST("category", categoryAdminApi.Add)
	adminProtected.DELETE("category", categoryAdminApi.Delete)
	adminProtected.DELETE("categorys", categoryAdminApi.DeleteMulti)
	adminProtected.POST("category/priority", categoryAdminApi.SetPriority)
}

func addSeries(
	config *config.Config,
	seriesApi *api.SeriesApi,
	seriesAdminApi *api.SeriesAdminApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
	adminProtected *gin.RouterGroup,
) {
	protected.GET("series", seriesApi.GetSeriesDetail)

	adminProtected.POST("series", seriesAdminApi.SaveOneOrUpdate)
	adminProtected.GET("series", seriesAdminApi.GetById)
	adminProtected.DELETE("series", seriesAdminApi.Delete)
	adminProtected.DELETE("serieses", seriesAdminApi.DeleteMulti)
	adminProtected.GET("serieses", seriesAdminApi.GetList)
	adminProtected.POST("series/seaturedContent", seriesAdminApi.SetFeaturedContent)
	adminProtected.POST("series/priority", seriesAdminApi.SetPriority)
}

func addResource(
	config *config.Config,
	resourceApi *api.ResourceApi,
	resourceAdminApi *api.ResourceAdminApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
	adminProtected *gin.RouterGroup,
) {
	protected.GET("resourceHome", resourceApi.GetResourceHome)
	protected.POST("resourceHomeItems", resourceApi.GetResourceHomeItems)

	// 获取所有资源
	adminProtected.GET("resources/all", resourceApi.GetAllResources)

	adminProtected.GET("resourceHome", resourceApi.GetResourceHome)
	adminProtected.GET("resource", resourceAdminApi.GetById)
	adminProtected.POST("resource", resourceAdminApi.Add)
	adminProtected.GET("resources", resourceAdminApi.GetResourceList)
	adminProtected.DELETE("resource", resourceAdminApi.Delete)
	adminProtected.DELETE("resources", resourceAdminApi.DeleteMulti)
	adminProtected.PUT("resource", resourceAdminApi.UpdateResource)
	adminProtected.POST("resource/seaturedContent", resourceAdminApi.SetFeaturedContent)
	adminProtected.POST("resource/priority", resourceAdminApi.SetPriority)

	// 资源标签相关路由
	adminProtected.GET("resource/tags", resourceApi.GetResourceTags)
	adminProtected.POST("resource/tags", resourceApi.SetResourceTags)
	adminProtected.POST("resource/tag", resourceApi.AddResourceTag)
	adminProtected.DELETE("resource/tag", resourceApi.RemoveResourceTag)
	adminProtected.POST("resources/byTags", resourceApi.GetResourcesByTags)
}

func addVideo(
	config *config.Config,
	videoApi *api.VideoApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
) {
	protected.POST("video/updateCutsomSubtitle", videoApi.UpdateCutsomSubtitle)
	protected.PUT("video/updateVideoDetail", videoApi.UpdateVideoDetail)
	protected.POST("video/detail", videoApi.GetVideoLocalDetail)
	protected.POST("video/changeResourceName", videoApi.ChangeResourceName)
}

func addNotes(
	config *config.Config,
	notesApi *api.NoteApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
) {
	protected.GET("notes", notesApi.GetLocalNoteList)
	protected.POST("note", notesApi.AddOrUpdateLocalNote)
	protected.DELETE("note", notesApi.DeleteLocalNote)
	protected.GET("note", notesApi.GetLocalNote)
}

func addCollect(
	config *config.Config,
	collectApi *api.CollectApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
) {
	collect := protected.Group("/collect/")
	{
		collect.POST("note", collectApi.AddNoteCollect)
		collect.DELETE("note", collectApi.DeleteLocalNoteCollect)
		collect.POST("sentence", collectApi.AddSentenceCollect)
		collect.DELETE("sentence", collectApi.RemoveSentenceCollect)
	}
}

func addAdmin(
	config *config.Config,
	_ *api.V1Api,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
	adminProtected *gin.RouterGroup,
) {
	// 管理员相关路由
}

func addUser(
	config *config.Config,
	userApi *api.UserApi,
	userAdminApi *api.UserAdminApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
	adminProtected *gin.RouterGroup,
) {
	// 公开路由
	rg.POST("admin/user/login", userAdminApi.LoginAdmin)
	rg.POST("user/login", userApi.Login)
	rg.POST("user/register", userApi.Register)
	rg.POST("user/loginByApple", userApi.LoginByApple)

	// 需要认证的路由
	protected.GET("user", userApi.GetById)
	protected.PUT("user", userApi.Update)

	// 用户相关路由组
	user := protected.Group("/user")
	{
		user.POST("logout", userApi.Logout)
		user.GET("config", userApi.FetchConfig)
		user.PUT("playerConfig", userApi.UpdateUserPlayerConfig)
		user.GET("watchHistory", userApi.GetWatchHistory)
		user.DELETE("watchHistory", userApi.DeleteWatchHistory)
		user.POST("deleteAccount", userApi.DeleteAccount)
		user.GET("benefits", userApi.GetUserBenefits)
		user.GET("benefits/group", userApi.GetBenefitsByGroup)
		user.POST("benefits/updateVideoLimit", userApi.UpdateUserVideoLimit)
		user.POST("benefits/updateAICallLimit", userApi.UpdateUserAICallLimit)
	}

	// 管理员路由
	adminProtected.DELETE("users", userAdminApi.DeleteMulti)
	adminProtected.DELETE("user", userAdminApi.Delete)
	adminProtected.GET("users", userAdminApi.GetList)
}

func addDataCenter(
	config *config.Config,
	datacenterApi *api.DataCenterApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
) {
	protected.POST("dataEpisode", datacenterApi.DataEpisodeEachAdd)
	protected.PUT("dataEpisode", datacenterApi.DataEpisodeUpdate)
	protected.GET("dataEpisodeHome", datacenterApi.DataEpisodeHome)
	protected.GET("dataEpisodeList", datacenterApi.DataEpisodeList)
	protected.GET("dataEpisodeLsData", datacenterApi.GetEpisodeLsData)
	protected.GET("dataEpisodeChart", datacenterApi.DataEpisodeByChart)
}

// func addAI(
// 	config *config.Config,
// 	_ *api.AIApi,
// 	rg *gin.RouterGroup,
// 	protected *gin.RouterGroup,
// ) {
// 	ai := protected.Group("/ai/")
// 	{
// 		// ai.POST("sentence", aiApi.ParseAIEnglishSentenceResult)
// 		// ai.POST("sentence", aiApi.StreamHandler)
// 	}
// }

func addVip(
	config *config.Config,
	vipApi *api.VipApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
	adminProtected *gin.RouterGroup,
) {
	// 公开路由
	rg.POST("vip/apple", vipApi.GetPayFromApple)
	rg.GET("vip/products", vipApi.GetProductList)

	// VIP 用户路由
	vip := protected.Group("/vip/")
	{
		vip.POST("createOrder", vipApi.CreatePayOrder)
		vip.GET("orderList", vipApi.GetOrderList)
		vip.GET("info", vipApi.GetUserVipInfo)
		vip.POST("exchangeCode", vipApi.ExchangeCode)
	}

	// VIP 管理路由
	adminProtected.GET("vip/promotionCodes", vipApi.GetPromotionCodeList)
	adminProtected.POST("vip/promotionCode", vipApi.AddPromotionCode)
	adminProtected.DELETE("vip/promotionCode", vipApi.DeletePromotionCode)
	adminProtected.DELETE("vip/promotionCodes", vipApi.DeleteMultiPromotionCode)
	adminProtected.POST("vip/exchangeCode", vipApi.ExchangeCode)
}

func addBenefit(
	config *config.Config,
	benefitApi *api.BenefitApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
	adminProtected *gin.RouterGroup,
) {
	// 权益管理相关路由
	adminProtected.GET("benefits", benefitApi.GetAllBenefits)
	adminProtected.POST("benefit", benefitApi.CreateBenefit)
	adminProtected.PUT("benefit", benefitApi.UpdateBenefit)

	// 权益组管理相关路由
	adminProtected.GET("benefit/groups", benefitApi.GetAllBenefitGroups)
	adminProtected.POST("benefit/group", benefitApi.CreateBenefitGroup)
	adminProtected.PUT("benefit/group", benefitApi.UpdateBenefitGroup)
	adminProtected.PUT("benefit/group/status", benefitApi.UpdateBenefitGroupStatus)
}

func addPlan(
	config *config.Config,
	planApi *api.PlanApi,
	rg *gin.RouterGroup,
	protected *gin.RouterGroup,
) {
	// 学习计划相关路由
	plan := protected.Group("/plan")
	{
		plan.GET("", planApi.GetPlan)
		plan.POST("generate", planApi.GeneratePlan)
		plan.POST("invalidate", planApi.InvalidatePlan)
		plan.GET("questionnaire", planApi.GetQuestionnaire)
	}
}
